"""
Orchestration-specific test fixtures and configuration.

This module provides fixtures specifically for testing the orchestration system,
including plugin manager setup, orchestration runner configuration, and
comprehensive workflow testing infrastructure with detailed setup/teardown procedures.
"""

import pytest
import asyncio
import logging
import tempfile
import shutil
import gc
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from unittest.mock import Mock

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from orchestrator.plugins import PluginManager, OrchestrationPlugin, get_plugin_manager
from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager
from orchestrator.query_processor import FinancialQueryProcessor, QueryType
from orchestrator.workflow_patterns import WorkflowPatternRegistry, WorkflowExecutor

# Import agent creation functions
from agents.mysql_agent import create_mysql_orchestrator_agent
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent

# Import test plugins
from tests.test_orchestration.plugins.sample_plugin import (
    SamplePlugin, DataTransformPlugin, LoggingPlugin
)
from tests.test_orchestration.plugins.failing_plugin import (
    InitializationFailingPlugin, ProcessingFailingPlugin, CleanupFailingPlugin,
    DependentPlugin, SlowPlugin
)

# Import service health checker
from tests.test_orchestration.service_health_checker import ServiceHealthChecker

logger = logging.getLogger(__name__)


# Global test state management
_test_session_state = {
    "plugin_managers": [],
    "orchestration_runners": [],
    "temp_directories": [],
    "active_contexts": [],
    "service_health_checked": False
}


@pytest.fixture(scope="session", autouse=True)
def session_setup_teardown():
    """Session-level setup and teardown for orchestration tests."""
    logger.info("Starting orchestration test session setup")

    # Pre-test service validation
    import asyncio
    health_checker = ServiceHealthChecker()
    health_results = asyncio.run(health_checker.run_comprehensive_health_check())

    if not health_results["overall_health"]["all_services_healthy"]:
        pytest.skip("Required services are not healthy - skipping orchestration tests")

    _test_session_state["service_health_checked"] = True
    logger.info("✓ All required services are healthy - proceeding with tests")

    yield

    # Session cleanup
    logger.info("Starting orchestration test session cleanup")

    # Cleanup all tracked resources
    asyncio.run(_cleanup_all_tracked_resources())

    # Force garbage collection
    gc.collect()

    logger.info("✓ Orchestration test session cleanup completed")


async def _cleanup_all_tracked_resources():
    """Cleanup all tracked test resources."""
    # Cleanup plugin managers
    for plugin_manager in _test_session_state["plugin_managers"]:
        try:
            await plugin_manager.cleanup_all_plugins()
        except Exception as e:
            logger.warning(f"Error cleaning up plugin manager: {e}")

    # Cleanup orchestration runners
    for runner in _test_session_state["orchestration_runners"]:
        try:
            if hasattr(runner, 'cleanup'):
                await runner.cleanup()
        except Exception as e:
            logger.warning(f"Error cleaning up orchestration runner: {e}")

    # Cleanup temporary directories
    for temp_dir in _test_session_state["temp_directories"]:
        try:
            if temp_dir.exists():
                shutil.rmtree(temp_dir, ignore_errors=True)
        except Exception as e:
            logger.warning(f"Error cleaning up temp directory {temp_dir}: {e}")

    # Clear active contexts
    for context_manager in _test_session_state["active_contexts"]:
        try:
            if hasattr(context_manager, 'clear_all_contexts'):
                context_manager.clear_all_contexts()
        except Exception as e:
            logger.warning(f"Error clearing contexts: {e}")

    # Clear tracking lists
    _test_session_state["plugin_managers"].clear()
    _test_session_state["orchestration_runners"].clear()
    _test_session_state["temp_directories"].clear()
    _test_session_state["active_contexts"].clear()


@pytest.fixture(autouse=True)
def test_setup_teardown():
    """Test-level setup and teardown for each test."""
    # Pre-test setup
    test_start_time = time.time()
    logger.debug("Starting individual test setup")

    # Clear any cached data
    if hasattr(gc, 'collect'):
        gc.collect()

    yield

    # Post-test cleanup
    test_duration = time.time() - test_start_time
    logger.debug(f"Test completed in {test_duration:.3f}s - starting cleanup")

    # Force cleanup of any test-specific resources
    import asyncio
    try:
        # Allow async operations to complete
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # Create a small delay task
            asyncio.create_task(asyncio.sleep(0.1))
    except Exception:
        pass
    gc.collect()


@pytest.fixture
def ensure_services_running():
    """Ensure all required services are running before test execution."""
    if not _test_session_state["service_health_checked"]:
        pytest.skip("Service health check not completed")

    return True


@pytest.fixture
def clean_plugin_manager():
    """Create a clean plugin manager with proper tracking and cleanup."""
    manager = PluginManager()

    # Track for cleanup
    _test_session_state["plugin_managers"].append(manager)

    return manager


@pytest.fixture
def clean_context_manager():
    """Create a clean context manager with proper tracking."""
    context_manager = ContextManager(persist_context=False)

    # Track for cleanup
    _test_session_state["active_contexts"].append(context_manager)

    yield context_manager

    # Immediate cleanup
    try:
        if hasattr(context_manager, 'clear_all_contexts'):
            context_manager.clear_all_contexts()
    except Exception as e:
        logger.warning(f"Error during context manager cleanup: {e}")


@pytest.fixture
def clean_temp_directory():
    """Create a clean temporary directory with proper tracking."""
    temp_dir = Path(tempfile.mkdtemp(prefix="orchestration_test_"))

    # Track for cleanup
    _test_session_state["temp_directories"].append(temp_dir)

    yield temp_dir

    # Immediate cleanup
    try:
        if temp_dir.exists():
            shutil.rmtree(temp_dir, ignore_errors=True)
    except Exception as e:
        logger.warning(f"Error cleaning up temp directory {temp_dir}: {e}")


@pytest.fixture
def plugin_manager(clean_plugin_manager):
    """Create a fresh plugin manager for each test."""
    return clean_plugin_manager


@pytest.fixture
def sample_plugins():
    """Create sample plugins for testing."""
    return {
        "sample": SamplePlugin(),
        "data_transform": DataTransformPlugin(),
        "logging": LoggingPlugin()
    }


@pytest.fixture
def mysql_agent_for_orchestration():
    """Create MySQL agent for orchestration testing."""
    try:
        from agents.mysql_agent import create_mysql_orchestrator_agent
        agent = create_mysql_orchestrator_agent()
        return agent
    except Exception as e:
        logger.warning(f"Could not create MySQL agent for orchestration testing: {e}")
        # Create mock agent for testing
        from unittest.mock import Mock
        mock_agent = Mock()
        mock_agent.name = "mock_mysql_agent"
        return mock_agent


@pytest.fixture
def orchestration_runner():
    """Create orchestration runner with mock agents for testing."""
    from unittest.mock import Mock

    # Create a mock orchestration runner
    mock_runner = Mock()
    mock_runner.mysql_agent = Mock()
    mock_runner.shortage_agent = Mock()
    mock_runner.alert_agent = Mock()
    mock_runner.context_manager = Mock()
    mock_runner.query_processor = Mock()
    mock_runner.pattern_registry = Mock()
    mock_runner.workflow_executor = Mock()
    mock_runner.orchestrator = Mock()

    # Set names for better debugging
    mock_runner.mysql_agent.name = "mock_mysql_agent"
    mock_runner.shortage_agent.name = "mock_shortage_agent"
    mock_runner.alert_agent.name = "mock_alert_agent"

    return mock_runner


@pytest.fixture
def financial_orchestrator():
    """Create financial orchestrator for testing."""
    from unittest.mock import Mock

    # Create a mock financial orchestrator
    mock_orchestrator = Mock()
    mock_orchestrator.name = "TestFinancialOrchestrator"
    mock_orchestrator.mysql_agent = Mock()
    mock_orchestrator.shortage_agent = Mock()
    mock_orchestrator.alert_agent = Mock()

    return mock_orchestrator


@pytest.fixture
def workflow_registry():
    """Create workflow pattern registry for testing."""
    from orchestrator.workflow_patterns import WorkflowPatternRegistry
    return WorkflowPatternRegistry()


@pytest.fixture
def workflow_executor(workflow_registry):
    """Create workflow executor for testing."""
    from orchestrator.workflow_patterns import WorkflowExecutor
    return WorkflowExecutor(workflow_registry)


@pytest.fixture
def failing_plugins():
    """Create failing plugins for error testing."""
    return {
        "init_fail": InitializationFailingPlugin(),
        "process_fail": ProcessingFailingPlugin(),
        "cleanup_fail": CleanupFailingPlugin(),
        "dependent": DependentPlugin(),
        "slow": SlowPlugin()
    }


@pytest.fixture
def temp_plugin_directory(clean_temp_directory):
    """Create temporary directory with test plugins."""
    temp_dir = clean_temp_directory

    # Copy test plugins to temp directory
    plugin_source_dir = Path(__file__).parent / "plugins"

    for plugin_file in plugin_source_dir.glob("*.py"):
        if not plugin_file.name.startswith("__"):
            shutil.copy2(plugin_file, temp_dir)

    return temp_dir


@pytest.fixture
async def mysql_agent_for_orchestration():
    """Create MySQL agent for orchestration testing."""
    try:
        agent = create_mysql_orchestrator_agent()
        
        # Initialize if needed
        if hasattr(agent, 'initialize_llm'):
            await agent.initialize_llm()
        
        yield agent
        
    except Exception as e:
        logger.warning(f"Could not create MySQL agent for orchestration testing: {e}")
        # Create mock agent for testing
        mock_agent = Mock()
        mock_agent.name = "mock_mysql_agent"
        yield mock_agent


@pytest.fixture
async def orchestration_runner(
    mysql_agent_for_orchestration,
    shortage_agent,
    alert_agent
):
    """Create orchestration runner with real agents."""
    try:
        # Create LLM factory mock
        def mock_llm_factory(agent):
            mock_llm = Mock()
            mock_llm.name = f"mock_llm_for_{agent.name if hasattr(agent, 'name') else 'unknown'}"
            return mock_llm
        
        runner = OrchestrationRunner(
            mysql_agent=mysql_agent_for_orchestration,
            shortage_agent=shortage_agent,
            alert_agent=alert_agent,
            llm_factory=mock_llm_factory,
            persist_context=False  # Don't persist for tests
        )
        
        yield runner
        
    except Exception as e:
        logger.error(f"Error creating orchestration runner: {e}")
        pytest.skip(f"Could not create orchestration runner: {e}")


@pytest.fixture
async def financial_orchestrator(
    mysql_agent_for_orchestration,
    shortage_agent,
    alert_agent
):
    """Create financial orchestrator for testing."""
    try:
        # Create LLM factory mock
        def mock_llm_factory(agent):
            mock_llm = Mock()
            mock_llm.name = f"mock_llm_for_{agent.name if hasattr(agent, 'name') else 'unknown'}"
            return mock_llm
        
        orchestrator = FinancialOrchestrator(
            llm_factory=mock_llm_factory,
            mysql_agent=mysql_agent_for_orchestration,
            shortage_agent=shortage_agent,
            alert_agent=alert_agent,
            name="TestFinancialOrchestrator"
        )
        
        yield orchestrator
        
    except Exception as e:
        logger.error(f"Error creating financial orchestrator: {e}")
        pytest.skip(f"Could not create financial orchestrator: {e}")


@pytest.fixture
def context_manager(clean_context_manager):
    """Create context manager for testing."""
    return clean_context_manager


@pytest.fixture
def query_processor():
    """Create query processor for testing."""
    return FinancialQueryProcessor()


@pytest.fixture
def workflow_registry():
    """Create workflow pattern registry for testing."""
    return WorkflowPatternRegistry()


@pytest.fixture
def workflow_executor(workflow_registry):
    """Create workflow executor for testing."""
    return WorkflowExecutor(workflow_registry)


@pytest.fixture
def financial_test_queries():
    """Provide realistic financial analysis queries for testing."""
    return {
        "shortage_analysis": {
            "query": "Analyze inventory shortages for Q4 2024",
            "expected_type": QueryType.SHORTAGE_ANALYSIS,
            "expected_agents": ["mysql_analyzer", "shortage_analyzer"]
        },
        "alert_generation": {
            "query": "Generate alerts for low stock items",
            "expected_type": QueryType.COMPREHENSIVE,
            "expected_agents": ["shortage_analyzer", "alert_manager"]
        },
        "cpu_shortage": {
            "query": "What are the current CPU shortage levels?",
            "expected_type": QueryType.SHORTAGE_ANALYSIS,
            "expected_agents": ["mysql_analyzer", "shortage_analyzer"]
        },
        "critical_notifications": {
            "query": "Send critical shortage notifications",
            "expected_type": QueryType.COMPREHENSIVE,
            "expected_agents": ["alert_manager"]
        },
        "comprehensive_analysis": {
            "query": "Comprehensive supply chain analysis with alerts",
            "expected_type": QueryType.COMPREHENSIVE,
            "expected_agents": ["mysql_analyzer", "shortage_analyzer", "alert_manager"]
        }
    }


@pytest.fixture
def expected_workflow_results():
    """Expected results structure for workflow validation."""
    return {
        "shortage_analysis": {
            "required_fields": [
                "shortage_index", "risk_level", "component_analysis"
            ],
            "mysql_data_fields": [
                "inventory_data", "historical_trends"
            ],
            "shortage_data_fields": [
                "shortage_calculations", "risk_assessment"
            ]
        },
        "alert_management": {
            "required_fields": [
                "alerts_sent", "notification_results", "alert_summary"
            ],
            "alert_data_fields": [
                "delivery_status", "channel_results"
            ]
        },
        "comprehensive_analysis": {
            "required_fields": [
                "mysql_results", "shortage_results", "alert_results",
                "workflow_summary", "execution_time"
            ]
        }
    }


@pytest.fixture
def orchestration_test_data():
    """Test data for orchestration workflows."""
    return {
        "mysql_sample_data": {
            "inventory_data": {
                "cpu": {"available": 1, "required": 2},
                "gpu": {"available": 2, "required": 6},
                "motherboard": {"available": 1, "required": 3},
                "fans": {"available": 1, "required": 6}
            },
            "historical_trends": {
                "cpu": {"trend": "declining", "forecast": "shortage"},
                "gpu": {"trend": "stable", "forecast": "adequate"}
            }
        },
        "shortage_sample_data": {
            "shortage_index": 0.625,
            "weighted_shortage_index": 0.82,
            "risk_level": "HIGH",
            "component_analysis": {
                "cpu": {"shortage_ratio": 0.5, "priority": "high"},
                "gpu": {"shortage_ratio": 0.67, "priority": "critical"}
            }
        },
        "alert_sample_data": {
            "alert_message": "CPU material shortage detected",
            "channels": ["HTTP", "EMAIL"],
            "severity": "HIGH",
            "delivery_config": {
                "http": {"url": "http://localhost:8080/alerts"},
                "email": {"recipient": "<EMAIL>"}
            }
        }
    }


@pytest.fixture
def plugin_manager_with_plugins(plugin_manager, sample_plugins):
    """Plugin manager pre-loaded with sample plugins."""
    # Register sample plugins
    for plugin_name, plugin in sample_plugins.items():
        success = plugin_manager.register_plugin(plugin)
        assert success, f"Failed to register plugin: {plugin_name}"

    yield plugin_manager

    # Cleanup is handled by the clean_plugin_manager fixture


@pytest.fixture
def mysql_agent_for_orchestration():
    """Create MySQL agent for orchestration testing."""
    try:
        from agents.mysql_agent import create_mysql_orchestrator_agent
        agent = create_mysql_orchestrator_agent()
        return agent
    except Exception as e:
        logger.warning(f"Could not create MySQL agent for orchestration testing: {e}")
        # Create mock agent for testing
        from unittest.mock import Mock
        mock_agent = Mock()
        mock_agent.name = "mock_mysql_agent"
        return mock_agent


@pytest.fixture
def orchestration_runner(mysql_agent_for_orchestration):
    """Create orchestration runner with real agents."""
    try:
        from orchestrator.orchestration_runner import OrchestrationRunner
        from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
        from agents.alert_manager_agent import create_alert_manager_agent
        from unittest.mock import Mock

        # Create agents
        shortage_agent = create_shortage_analyzer_agent()
        alert_agent = create_alert_manager_agent()

        # Create LLM factory mock
        def mock_llm_factory(agent):
            mock_llm = Mock()
            mock_llm.name = f"mock_llm_for_{agent.name if hasattr(agent, 'name') else 'unknown'}"
            return mock_llm

        runner = OrchestrationRunner(
            mysql_agent=mysql_agent_for_orchestration,
            shortage_agent=shortage_agent,
            alert_agent=alert_agent,
            llm_factory=mock_llm_factory,
            persist_context=False  # Don't persist for tests
        )

        return runner

    except Exception as e:
        logger.error(f"Error creating orchestration runner: {e}")
        pytest.skip(f"Could not create orchestration runner: {e}")


@pytest.fixture
def financial_orchestrator(mysql_agent_for_orchestration):
    """Create financial orchestrator for testing."""
    try:
        from orchestrator.financial_orchestrator import FinancialOrchestrator
        from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
        from agents.alert_manager_agent import create_alert_manager_agent
        from unittest.mock import Mock

        # Create agents
        shortage_agent = create_shortage_analyzer_agent()
        alert_agent = create_alert_manager_agent()

        # Create LLM factory mock
        def mock_llm_factory(agent):
            mock_llm = Mock()
            mock_llm.name = f"mock_llm_for_{agent.name if hasattr(agent, 'name') else 'unknown'}"
            return mock_llm

        orchestrator = FinancialOrchestrator(
            llm_factory=mock_llm_factory,
            mysql_agent=mysql_agent_for_orchestration,
            shortage_agent=shortage_agent,
            alert_agent=alert_agent,
            name="TestFinancialOrchestrator"
        )

        return orchestrator

    except Exception as e:
        logger.error(f"Error creating financial orchestrator: {e}")
        pytest.skip(f"Could not create financial orchestrator: {e}")


@pytest.fixture
def workflow_registry():
    """Create workflow pattern registry for testing."""
    from orchestrator.workflow_patterns import WorkflowPatternRegistry
    return WorkflowPatternRegistry()


@pytest.fixture
def workflow_executor(workflow_registry):
    """Create workflow executor for testing."""
    from orchestrator.workflow_patterns import WorkflowExecutor
    return WorkflowExecutor(workflow_registry)


@pytest.fixture
def orchestration_performance_monitor():
    """Enhanced performance monitoring fixture for orchestration tests."""
    import time
    import psutil
    import threading

    class EnhancedPerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.start_memory = None
            self.end_memory = None
            self.peak_memory = None
            self.metrics = {}
            self.monitoring_thread = None
            self.monitoring_active = False
            self.memory_samples = []
            self.cpu_samples = []

        def start_monitoring(self):
            self.start_time = time.time()
            self.monitoring_active = True
            self.memory_samples = []
            self.cpu_samples = []

            try:
                process = psutil.Process()
                self.start_memory = process.memory_info().rss / 1024 / 1024  # MB
                self.peak_memory = self.start_memory

                # Start continuous monitoring thread
                self.monitoring_thread = threading.Thread(target=self._continuous_monitoring)
                self.monitoring_thread.daemon = True
                self.monitoring_thread.start()

            except Exception as e:
                logger.warning(f"Could not start performance monitoring: {e}")
                self.start_memory = None

        def stop_monitoring(self):
            self.end_time = time.time()
            self.monitoring_active = False

            try:
                process = psutil.Process()
                self.end_memory = process.memory_info().rss / 1024 / 1024  # MB

                # Wait for monitoring thread to finish
                if self.monitoring_thread and self.monitoring_thread.is_alive():
                    self.monitoring_thread.join(timeout=1.0)

            except Exception as e:
                logger.warning(f"Could not stop performance monitoring: {e}")
                self.end_memory = None

        def _continuous_monitoring(self):
            """Continuous monitoring in background thread."""
            try:
                process = psutil.Process()
                while self.monitoring_active:
                    try:
                        memory_mb = process.memory_info().rss / 1024 / 1024
                        cpu_percent = process.cpu_percent()

                        self.memory_samples.append(memory_mb)
                        self.cpu_samples.append(cpu_percent)

                        if memory_mb > self.peak_memory:
                            self.peak_memory = memory_mb

                        time.sleep(0.1)  # Sample every 100ms

                    except Exception:
                        break
            except Exception:
                pass

        def get_metrics(self):
            elapsed = self.end_time - self.start_time if self.start_time and self.end_time else None
            memory_delta = (
                self.end_memory - self.start_memory
                if self.start_memory and self.end_memory
                else None
            )

            # Calculate additional metrics
            avg_memory = sum(self.memory_samples) / len(self.memory_samples) if self.memory_samples else None
            max_cpu = max(self.cpu_samples) if self.cpu_samples else None
            avg_cpu = sum(self.cpu_samples) / len(self.cpu_samples) if self.cpu_samples else None

            return {
                "elapsed_time": elapsed,
                "memory_delta_mb": memory_delta,
                "start_memory_mb": self.start_memory,
                "end_memory_mb": self.end_memory,
                "peak_memory_mb": self.peak_memory,
                "avg_memory_mb": avg_memory,
                "max_cpu_percent": max_cpu,
                "avg_cpu_percent": avg_cpu,
                "memory_samples_count": len(self.memory_samples),
                "cpu_samples_count": len(self.cpu_samples)
            }

    return EnhancedPerformanceMonitor()


# Enhanced logging configuration
@pytest.fixture(autouse=True)
def configure_detailed_logging(request):
    """Configure detailed logging for orchestration tests."""
    # Create test-specific logger
    test_name = request.node.name
    test_logger = logging.getLogger(f"orchestration_test.{test_name}")

    # Configure log level based on test markers
    if request.node.get_closest_marker("performance"):
        log_level = logging.WARNING  # Reduce noise for performance tests
    elif request.node.get_closest_marker("end_to_end"):
        log_level = logging.INFO  # Detailed info for E2E tests
    else:
        log_level = logging.DEBUG  # Full debug for other tests

    test_logger.setLevel(log_level)

    # Add test-specific handler if not exists
    handler_name = f"test_handler_{test_name}"
    if not any(h.name == handler_name for h in test_logger.handlers):
        handler = logging.StreamHandler()
        handler.name = handler_name
        handler.setLevel(log_level)
        formatter = logging.Formatter(
            f'%(asctime)s - {test_name} - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        test_logger.addHandler(handler)

    # Log test start
    test_logger.info(f"Starting test: {test_name}")

    yield test_logger

    # Log test completion
    test_logger.info(f"Completed test: {test_name}")

    # Cleanup handler
    for handler in test_logger.handlers[:]:
        if handler.name == handler_name:
            test_logger.removeHandler(handler)
            handler.close()


@pytest.fixture
def data_flow_logger():
    """Logger specifically for tracking data flow between agents."""
    flow_logger = logging.getLogger("orchestration.data_flow")
    flow_logger.setLevel(logging.INFO)

    # Add handler for data flow logging
    if not flow_logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - DATA_FLOW - %(message)s'
        )
        handler.setFormatter(formatter)
        flow_logger.addHandler(handler)

    return flow_logger


@pytest.fixture
def plugin_event_logger():
    """Logger specifically for tracking plugin processing events."""
    plugin_logger = logging.getLogger("orchestration.plugin_events")
    plugin_logger.setLevel(logging.INFO)

    # Add handler for plugin event logging
    if not plugin_logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - PLUGIN_EVENT - %(message)s'
        )
        handler.setFormatter(formatter)
        plugin_logger.addHandler(handler)

    return plugin_logger


# Test markers for orchestration tests
def pytest_configure(config):
    """Configure pytest markers for orchestration tests."""
    config.addinivalue_line(
        "markers", "orchestration: mark test as orchestration system test"
    )
    config.addinivalue_line(
        "markers", "plugin_test: mark test as plugin system test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "end_to_end: mark test as end-to-end workflow test"
    )
    config.addinivalue_line(
        "markers", "real_service: mark test as requiring real services"
    )
    config.addinivalue_line(
        "markers", "error_handling: mark test as error handling test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
