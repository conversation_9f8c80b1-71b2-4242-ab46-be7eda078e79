from openai import OpenAI
# Set OpenAI's API key and API base to use vLLM's API server.
openai_api_key = "EMPTY"
openai_api_base = "http://192.168.1.54:38701/v1"
# openai_api_base = "http://192.168.1.54:8701/v1"
client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,  
)

chat_response = client.chat.completions.create(
    model="Qwen/Qwen3-32B",
    messages=[
        {"role": "user", "content": "Give me a short introduction to large language models."},
    ],
    timeout=20.0,  # Set a timeout of 30 seconds
    max_tokens=32768,
    temperature=0.6,
    top_p=0.95,
    extra_body={
        "top_k": 20,
    },
)
print("Chat response:", chat_response)